import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/app_table.dart';
import 'package:octasync_client/imports.dart';

class SeparatedEmployeePage extends StatefulWidget {
  const SeparatedEmployeePage({super.key});

  @override
  State<SeparatedEmployeePage> createState() => _SeparatedEmployeePageState();
}

class _SeparatedEmployeePageState extends State<SeparatedEmployeePage> {
  late AppTableStateManage _stateManage;
  List<AppTableColumn> get colsTemp => [
    AppTableColumn(
      text: '姓名',
      field: 'Name',
      type: AppTableColumnType.text(),
      width: 120,
      resizable: true, // 允许调整列宽
      frozen: AppTableColumnFrozen.start,
    ),
    AppTableColumn(
      text: '离职日期',
      field: 'Date',
      type: AppTableColumnType.text(),
      width: 120,
      resizable: true, // 允许调整列宽
      frozen: AppTableColumnFrozen.start,
    ),
    AppTableColumn(
      text: '交接状态',
      field: 'Handover',
      type: AppTableColumnType.text(),
      width: 120,
      resizable: true, // 允许调整列宽
      frozen: AppTableColumnFrozen.start,
    ),
    AppTableColumn(
      text: '操作',
      field: 'Opt',
      type: AppTableColumnType.text(),
      width: 80,
      resizable: false, // 允许调整列宽
      showMore: false,
      cellBuilder: (context, value, column, row) {
        return AppButton(
          text: '离职交接',
          type: ButtonType.primary,
          textOnly: true,
          onPressed: () {
            showDepartmentDialog(context);
          },
        );
      },
    ),
  ];

  /// 数据交接弹窗
  void showDepartmentDialog(BuildContext context) async {
    const mockData = [
      {'resource': '项目管理', 'describe': '转让离职成员创建及负责管理的所有项目、阶段', 'employee': null},
      {'resource': '项目管理', 'describe': '转让离职成员创建及负责管理的所有项目、阶段', 'employee': null},
      {'resource': '项目管理', 'describe': '转让离职成员创建及负责管理的所有项目、阶段', 'employee': null},
      {'resource': '项目管理', 'describe': '转让离职成员创建及负责管理的所有项目、阶段', 'employee': null},
      {'resource': '项目管理', 'describe': '转让离职成员创建及负责管理的所有项目、阶段', 'employee': null},
      {'resource': '项目管理', 'describe': '转让离职成员创建及负责管理的所有项目、阶段', 'employee': null},
    ];

    /// 表格行
    Widget buildTableRow(item) {
      return SizedBox(
        height: 40,
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.all(5),
              child: SizedBox(width: 150, child: Text(item['resource'] ?? '')),
            ),
            VerticalDivider(),
            Expanded(
              child: Padding(padding: const EdgeInsets.all(5), child: Text(item['describe'] ?? '')),
            ),
            VerticalDivider(),
            Padding(
              padding: const EdgeInsets.all(5),
              child: SizedBox(width: 150, child: Text(item['employee'] ?? '人')),
            ),
          ],
        ),
      );
    }

    AppDialog.show(
      width: 720,
      height: 528,
      context: context,
      title: '数据交接',
      slideDirection: SlideDirection.right,
      showFooter: false,
      child: StatefulBuilder(
        builder: (BuildContext context, StateSetter setDialogState) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('离职员工', style: Theme.of(context).textTheme.headlineMedium),
              SizedBox(height: 10),
              Row(
                spacing: 10,
                children: [
                  ImageCacheUtil.cachedAvatarImage(
                    imageUrl: null,
                    size: 48,
                    radius: AppRadiusSize.radius6,
                  ),
                  Column(
                    spacing: 5,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('张三', style: Theme.of(context).textTheme.headlineMedium),
                      Text('销售运营中心', style: Theme.of(context).textTheme.labelMedium),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 20),
              Text('设置接收人', style: Theme.of(context).textTheme.headlineMedium),
              SizedBox(height: 10),

              // 表格
              Container(
                width: double.infinity,
                height: 300,
                decoration: BoxDecoration(
                  border: Border.all(color: context.border300),
                  borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
                ),
                // child: Column(children: mockData.map((item) => buildTableRow(item)).toList()),
                child: ListView.separated(
                  itemCount: mockData.length, // 列表项的数量
                  itemBuilder: (context, index) {
                    final item = mockData[index];
                    return buildTableRow(item);
                  },
                  separatorBuilder: (context, index) {
                    return Divider(height: 0.5);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: AppTable(
            uniqueId: 'Id',
            showAddRowButton: false,
            showAddColumnButton: false,
            checkType: TableCheckedEnum.none,
            indexColumnWidth: 100,
            onLoaded: (stateManage) {
              _stateManage = stateManage;
              _stateManage.setColumns(colsTemp);

              final arr = [
                {'Id': '1', 'Name': '张三', 'Date': '2021-01-01', 'Handover': '未交接'},
              ];
              var rows = _stateManage.convertToTreeData(arr);

              _stateManage.setRows(rows);
            },
          ),
        ),
      ],
    );
  }
}
